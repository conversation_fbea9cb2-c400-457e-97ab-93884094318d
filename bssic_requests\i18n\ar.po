# Translation of Odoo Server.
# This file contains the translation of the following modules:
#	* bssic_requests
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-01-01 12:00+0000\n"
"PO-Revision-Date: 2024-01-01 12:00+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: bssic_requests
#: model:bssic.request.type,name:bssic_requests.request_type_authorization_delegation
msgid "Authorization Delegation"
msgstr "تفويض صلاحية في سقف المستخدم"

#. module: bssic_requests
#: model_terms:ir.ui.view,arch_db:bssic_requests.view_authorization_delegation_request_form
msgid "Authorization Delegation Request"
msgstr "طلب تفويض صلاحية في سقف المستخدم"

#. module: bssic_requests
#: model_terms:ir.ui.view,arch_db:bssic_requests.view_authorization_delegation_request_form
msgid "Authorization Delegation Details"
msgstr "تفاصيل تفويض الصلاحية"

#. module: bssic_requests
#: model_terms:ir.ui.view,arch_db:bssic_requests.view_authorization_delegation_request_tree
msgid "Authorization Delegation Requests"
msgstr "طلبات تفويض الصلاحية"

#. module: bssic_requests
#: model_terms:ir.actions.act_window,name:bssic_requests.action_authorization_delegation_request
msgid "Authorization Delegation Requests"
msgstr "طلبات تفويض الصلاحية"

#. module: bssic_requests
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_request__ceiling_reason
msgid "Reason for Ceiling Increase"
msgstr "سبب رفع السقف"

#. module: bssic_requests
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_request__delegation_details
msgid "Details"
msgstr "التفاصيل"

#. module: bssic_requests
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_request__delegation_from_date
msgid "From Date"
msgstr "التاريخ من"

#. module: bssic_requests
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_request__delegation_to_date
msgid "To Date"
msgstr "التاريخ إلى"

#. module: bssic_requests
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_request__delegation_max_amount
msgid "Max. Amount"
msgstr "الحد الأقصى للمبلغ"

#. module: bssic_requests
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_request__delegation_auth_limit
msgid "Auth O.D. Limit"
msgstr "حد التفويض"

#. module: bssic_requests
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_request__show_authorization_delegation_fields
msgid "Show Authorization Delegation Fields"
msgstr "إظهار حقول تفويض الصلاحية"

#. module: bssic_requests
#: model_terms:ir.ui.view,arch_db:bssic_requests.view_authorization_delegation_request_form
msgid "Create your first Authorization Delegation Request!"
msgstr "أنشئ أول طلب تفويض صلاحية!"

#. module: bssic_requests
#: model_terms:ir.ui.view,arch_db:bssic_requests.view_authorization_delegation_request_form
msgid "Submit requests for authorization delegation in user ceiling limits."
msgstr "قدم طلبات تفويض الصلاحية في حدود سقف المستخدم."

#. module: bssic_requests
#: code:addons/bssic_requests/models/request.py:685
msgid "Reason for Ceiling Increase is required for Authorization Delegation requests."
msgstr "سبب رفع السقف مطلوب لطلبات تفويض الصلاحية."

#. module: bssic_requests
#: code:addons/bssic_requests/models/request.py:689
msgid "Details are required for Authorization Delegation requests."
msgstr "التفاصيل مطلوبة لطلبات تفويض الصلاحية."

#. module: bssic_requests
#: code:addons/bssic_requests/models/request.py:693
msgid "From Date is required for Authorization Delegation requests."
msgstr "التاريخ من مطلوب لطلبات تفويض الصلاحية."

#. module: bssic_requests
#: code:addons/bssic_requests/models/request.py:697
msgid "To Date is required for Authorization Delegation requests."
msgstr "التاريخ إلى مطلوب لطلبات تفويض الصلاحية."

#. module: bssic_requests
#: code:addons/bssic_requests/models/request.py:701
msgid "Max. Amount must be greater than zero for Authorization Delegation requests."
msgstr "الحد الأقصى للمبلغ يجب أن يكون أكبر من الصفر لطلبات تفويض الصلاحية."

#. module: bssic_requests
#: code:addons/bssic_requests/models/request.py:705
msgid "Auth O.D. Limit must be greater than zero for Authorization Delegation requests."
msgstr "حد التفويض يجب أن يكون أكبر من الصفر لطلبات تفويض الصلاحية."

#. module: bssic_requests
#: code:addons/bssic_requests/models/request.py:710
msgid "To Date must be after From Date in Authorization Delegation requests."
msgstr "التاريخ إلى يجب أن يكون بعد التاريخ من في طلبات تفويض الصلاحية."

#. module: bssic_requests
#: model:bssic.request.type,name:bssic_requests.request_type_free_entry
msgid "Free Chain"
msgstr "القيد الحر"

#. module: bssic_requests
#: model_terms:ir.ui.view,arch_db:bssic_requests.view_free_entry_request_form
msgid "Free Chain Request"
msgstr "طلب القيد الحر"

#. module: bssic_requests
#: model_terms:ir.ui.view,arch_db:bssic_requests.view_free_entry_request_form
msgid "Free Chain Details"
msgstr "تفاصيل القيد الحر"

#. module: bssic_requests
#: model_terms:ir.ui.view,arch_db:bssic_requests.view_free_entry_request_tree
msgid "Free Chain Requests"
msgstr "طلبات القيد الحر"

#. module: bssic_requests
#: model_terms:ir.actions.act_window,name:bssic_requests.action_free_entry_request
msgid "Free Chain Requests"
msgstr "طلبات القيد الحر"

#. module: bssic_requests
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_request__free_entry_subject
msgid "Subject"
msgstr "الموضوع"

#. module: bssic_requests
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_request__free_entry_details
msgid "Operation Details"
msgstr "تفاصيل العملية"

#. module: bssic_requests
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_request__show_free_entry_fields
msgid "Show Free Entry Fields"
msgstr "إظهار حقول القيد الحر"

#. module: bssic_requests
#: model_terms:ir.ui.view,arch_db:bssic_requests.view_free_entry_request_form
msgid "Create your first Free Chain Request!"
msgstr "أنشئ أول طلب قيد حر!"

#. module: bssic_requests
#: model_terms:ir.ui.view,arch_db:bssic_requests.view_free_entry_request_form
msgid "Submit requests for free chain authorization."
msgstr "قدم طلبات تفويض القيد الحر."

#. module: bssic_requests
#: code:addons/bssic_requests/models/request.py:729
msgid "Subject is required for Free Chain requests."
msgstr "الموضوع مطلوب لطلبات القيد الحر."

#. module: bssic_requests
#: code:addons/bssic_requests/models/request.py:733
msgid "Operation Details are required for Free Chain requests."
msgstr "تفاصيل العملية مطلوبة لطلبات القيد الحر."
