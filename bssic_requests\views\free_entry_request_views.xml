<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Free Entry Request Form View -->
        <record id="view_free_entry_request_form" model="ir.ui.view">
            <field name="name">free.entry.request.form</field>
            <field name="model">bssic.request</field>
            <field name="arch" type="xml">
                <form string="Free Chain Request">
                    <header>
                        <button name="action_submit" string="Submit" type="object"
                                class="oe_highlight" states="draft"/>
                        <button name="action_approve_direct_manager" string="Approve (Direct Manager)"
                                type="object" class="oe_highlight" states="direct_manager"
                                groups="bssic_requests.group_bssic_direct_manager"/>
                        <button name="action_approve_audit_manager" string="Approve (Audit Manager)"
                                type="object" class="oe_highlight" states="audit_manager"
                                groups="bssic_requests.group_bssic_audit_manager"/>
                        <button name="action_approve_it_manager" string="Approve (IT Manager)"
                                type="object" class="oe_highlight" states="it_manager"
                                groups="bssic_requests.group_bssic_it_manager"/>
                        <button name="action_assign" string="Assign to IT Staff"
                                type="object" class="oe_highlight" states="assigned"
                                groups="bssic_requests.group_bssic_it_manager"/>
                        <button name="action_complete" string="Mark as Completed"
                                type="object" class="oe_highlight" states="in_progress"
                                groups="bssic_requests.group_bssic_it_staff"/>
                        <button name="action_reject" string="Reject" type="object"
                                class="oe_reject" states="direct_manager,audit_manager,it_manager"
                                groups="bssic_requests.group_bssic_direct_manager,bssic_requests.group_bssic_audit_manager,bssic_requests.group_bssic_it_manager"/>
                        <field name="state" widget="statusbar" statusbar_visible="draft,submitted,direct_manager,audit_manager,it_manager,assigned,in_progress,completed"/>
                    </header>
                    <sheet>
                        <div class="oe_title">
                            <h1>
                                <field name="name" readonly="1"/>
                            </h1>
                        </div>
                        <group>
                            <group>
                                <field name="request_type_id" readonly="1"/>
                                <field name="employee_number" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                <field name="employee_id" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                <field name="department_id"/>
                                <field name="job_id"/>
                            </group>
                            <group>
                                <field name="request_date" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                <field name="assigned_to" attrs="{'readonly': [('state', 'not in', ['assigned'])]}"
                                       groups="bssic_requests.group_bssic_it_manager"/>
                            </group>
                        </group>

                        <!-- Free Chain Specific Fields -->
                        <group string="Free Chain Details" attrs="{'invisible': [('show_free_entry_fields', '=', False)]}">
                            <group>
                                <field name="free_entry_subject" string="Subject" required="1" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                            </group>
                            <group>
                                <field name="free_entry_details" string="Operation Details" required="1" attrs="{'readonly': [('state', '!=', 'draft')]}" widget="text"/>
                            </group>
                        </group>

                        <group string="Description">
                            <field name="description" nolabel="1" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                        </group>

                        <!-- Rejection and Completion Notes -->
                        <group string="Notes" attrs="{'invisible': [('state', 'in', ['draft', 'submitted', 'direct_manager', 'audit_manager', 'it_manager', 'assigned'])]}">
                            <field name="rejection_reason" attrs="{'invisible': [('state', '!=', 'rejected')]}"/>
                            <field name="completion_notes" attrs="{'invisible': [('state', 'not in', ['in_progress', 'completed'])], 'readonly': [('state', '=', 'completed')]}"/>
                        </group>

                        <!-- Hidden fields for computation -->
                        <field name="show_free_entry_fields" invisible="1"/>
                        <field name="request_type_code" invisible="1"/>
                    </sheet>
                    <div class="oe_chatter">
                        <field name="message_follower_ids" widget="mail_followers"/>
                        <field name="activity_ids" widget="mail_activity"/>
                        <field name="message_ids" widget="mail_thread"/>
                    </div>
                </form>
            </field>
        </record>

        <!-- Free Entry Request Tree View -->
        <record id="view_free_entry_request_tree" model="ir.ui.view">
            <field name="name">free.entry.request.tree</field>
            <field name="model">bssic.request</field>
            <field name="arch" type="xml">
                <tree string="Free Chain Requests">
                    <field name="name"/>
                    <field name="employee_id"/>
                    <field name="department_id"/>
                    <field name="request_date"/>
                    <field name="free_entry_subject"/>
                    <field name="state" decoration-info="state=='draft'"
                           decoration-warning="state in ['submitted','direct_manager','audit_manager','it_manager']"
                           decoration-success="state=='completed'"
                           decoration-danger="state=='rejected'"/>
                </tree>
            </field>
        </record>

        <!-- Free Entry Request Search View -->
        <record id="view_free_entry_request_search" model="ir.ui.view">
            <field name="name">free.entry.request.search</field>
            <field name="model">bssic.request</field>
            <field name="arch" type="xml">
                <search string="Free Chain Requests">
                    <field name="name"/>
                    <field name="employee_id"/>
                    <field name="department_id"/>
                    <field name="free_entry_subject"/>
                    <field name="free_entry_details"/>
                    <separator/>
                    <filter string="My Requests" name="my_requests"
                            domain="[('employee_id.user_id', '=', uid)]"/>
                    <filter string="Draft" name="draft" domain="[('state', '=', 'draft')]"/>
                    <filter string="Pending Approval" name="pending"
                            domain="[('state', 'in', ['submitted', 'direct_manager', 'audit_manager', 'it_manager'])]"/>
                    <filter string="In Progress" name="in_progress" domain="[('state', '=', 'in_progress')]"/>
                    <filter string="Completed" name="completed" domain="[('state', '=', 'completed')]"/>
                    <filter string="Rejected" name="rejected" domain="[('state', '=', 'rejected')]"/>
                    <separator/>
                    <group expand="0" string="Group By">
                        <filter string="Employee" name="group_employee" context="{'group_by': 'employee_id'}"/>
                        <filter string="Department" name="group_department" context="{'group_by': 'department_id'}"/>
                        <filter string="Status" name="group_state" context="{'group_by': 'state'}"/>
                        <filter string="Request Date" name="group_date" context="{'group_by': 'request_date'}"/>
                    </group>
                </search>
            </field>
        </record>

        <!-- Free Entry Request Action -->
        <record id="action_free_entry_request" model="ir.actions.act_window">
            <field name="name">Free Chain Requests</field>
            <field name="res_model">bssic.request</field>
            <field name="view_mode">tree,form</field>
            <field name="domain">[('request_type_id.code', '=', 'free_entry')]</field>
            <field name="context">{
                'default_request_type_code': 'free_entry',
                'search_default_my_requests': 1
            }</field>
            <field name="view_ids" eval="[(5, 0, 0),
                (0, 0, {'view_mode': 'tree', 'view_id': ref('view_free_entry_request_tree')}),
                (0, 0, {'view_mode': 'form', 'view_id': ref('view_free_entry_request_form')})]"/>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create your first Free Chain Request!
                </p>
                <p>
                    Submit requests for free chain authorization.
                </p>
            </field>
        </record>

        <!-- Menu Item for Free Entry Requests -->
        <menuitem id="menu_free_entry_request"
                  name="Free Chain"
                  parent="menu_bssic_request"
                  action="action_free_entry_request"
                  sequence="70"/>

    </data>
</odoo>
